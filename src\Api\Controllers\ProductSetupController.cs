using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Api.DTOs;

namespace Api.Controllers;

[ApiController]
[Route("api/product-setup")]
public class ProductSetupController(IProductService productService, ILogger<ProductSetupController> logger) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateProduct([FromBody] CreateProductDto createProductDto)
    {
        try
        {
            var product = new Domain.Entities.Product
            {
                Name = createProductDto.Name,
                Benefits = createProductDto.Benefits,
                SocialProof = createProductDto.SocialProof,
                Metodology = createProductDto.Metodology,
                Guarantee = createProductDto.Guarantee,
                Deliverables = createProductDto.Deliverables,
                CustomerJourney = createProductDto.CustomerJourney
            };

            var createdProduct = await productService.CreateProductAsync(product);
            return CreatedAtAction(nameof(GetProduct), new { identifier = createdProduct.ExternalId }, createdProduct);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while creating the product.");
            return StatusCode(500, new { message = "An error occurred while creating the product.", details = ex.Message });
        }
    }

    [HttpGet("{identifier}/template")]
    public async Task<IActionResult> GenerateSetupTemplate(string identifier)
    {
        try
        {
            logger.LogInformation("Generating product setup template for identifier: {Identifier}", identifier);
            
            var template = await productService.GenerateSetupTemplateAsync(identifier);
            
            return Ok(new SetupTemplateResponse
            {
                Success = true,
                Template = template,
                Message = "Product setup template generated successfully",
                EntityType = "Product",
                Identifier = identifier
            });
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "Product not found with identifier: {Identifier}", identifier);
            return NotFound(new SetupTemplateResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating product setup template for identifier: {Identifier}", identifier);
            return StatusCode(500, new SetupTemplateResponse
            {
                Success = false,
                Message = "Internal server error while generating template"
            });
        }
    }

    [HttpGet("{identifier}")]
    public async Task<IActionResult> GetProduct(string identifier)
    {
        try
        {
            var product = await productService.GetByIdOrExternalIdAsync(identifier);
            return Ok(product);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { Message = ex.Message });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving product with identifier: {Identifier}", identifier);
            return StatusCode(500, new { Message = "Internal server error" });
        }
    }
}