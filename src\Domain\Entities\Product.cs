using Domain.Enums.Products;

namespace Domain.Entities;

using Domain.Interfaces;
using Google.Cloud.Firestore;

[FirestoreData]
public sealed class Product : IEntity
{
    [FirestoreProperty]
    public int Id { get; set; }
     
    [FirestoreProperty]
    public string ExternalId { get; set; } = string.Empty;
     
    [FirestoreProperty]
    public required string Name { get; set; }

    [FirestoreProperty]
    public required string Benefits { get; set; }

    [FirestoreProperty]
    public required string SocialProof { get; set; }

    [FirestoreProperty]
    public required string Metodology { get; set; }

    [FirestoreProperty]
    public required string Guarantee { get; set; }

    [FirestoreProperty]
    public required ProductDeliverablesEnum Deliverables  { get; set; }
     
    [FirestoreProperty]
    public required ProductCustomerJourneyEnum CustomerJourney { get; set; }
}