using Domain.Entities;
using Domain.Interfaces;
using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Application.Services;

public class ContentService(
    IAiOrchestrator aiOrchestrator,
    IFileReaderService fileReaderService,
    IPromptGenerationService promptGenerationService,
    ILogger<ContentService> logger) : IContentService
{
    public async Task<string> GenerateContentAsync(string prompt, string? preferredProvider = null)
    {
        logger.LogInformation("Generating content for prompt with preferred provider: {PreferredProvider}", preferredProvider ?? "default");
        return await aiOrchestrator.GenerateContentAsync(prompt, preferredProvider);
    }

    public async Task<string> AnalyzeFileAsync(IFormFile file)
    {
        logger.LogInformation("Analyzing file: {FileName}", file.FileName);
        
        if (!fileReaderService.IsFileTypeSupported(file.FileName))
        {
            var supportedExtensions = string.Join(", ", fileReaderService.GetSupportedExtensions());
            throw new NotSupportedException($"File type not supported. Supported extensions: {supportedExtensions}");
        }

        string fileContent;
        await using (var stream = file.OpenReadStream())
        {
            fileContent = await fileReaderService.ReadFileAsync(stream, file.FileName);
        }

        if (string.IsNullOrWhiteSpace(fileContent))
        {
            throw new InvalidOperationException("Could not extract content from the file or file is empty.");
        }

        var prompt = $@"Analyze the following file and provide a detailed explanation about:
                1. What type of file this is
                2. What is the main content
                3. What could be the purpose or use of this file
                4. Summary of the most important data/information

                File name: {file.FileName}
                Size: {file.Length} bytes

                File content:
                {fileContent}

                Please provide a clear and structured analysis in English.";

        return await aiOrchestrator.GenerateContentAsync(prompt);
    }

    public async Task<string> AnalyzeSetupAsync(string templateType, object entity, string? context = null)
    {
        logger.LogInformation("Analyzing {TemplateType} setup using AI", templateType);
        
        var analysisPrompt = await promptGenerationService.GenerateAnalysisPromptAsync(templateType, entity, context);
        return await aiOrchestrator.GenerateContentAsync(analysisPrompt);
    }

    public async Task<string> GenerateImprovementSuggestionsAsync(string templateType, object entity)
    {
        logger.LogInformation("Generating improvement suggestions for {TemplateType}", templateType);
        
        var improvementPrompt = await promptGenerationService.GenerateImprovementPromptAsync(templateType, entity);
        return await aiOrchestrator.GenerateContentAsync(improvementPrompt);
    }

    public async Task<string> ValidateSetupCompletenessAsync(string templateType, object entity)
    {
        logger.LogInformation("Validating setup completeness for {TemplateType}", templateType);
        
        var validationPrompt = await promptGenerationService.GenerateValidationPromptAsync(templateType, entity);
        return await aiOrchestrator.GenerateContentAsync(validationPrompt);
    }

    public async Task<string> GenerateContentFromTemplateAsync(string templateType, object entity, string contentRequest, string? context = null)
    {
        logger.LogInformation("Generating {ContentRequest} content for {TemplateType}", contentRequest, templateType);
        
        var analysisPrompt = await promptGenerationService.GenerateAnalysisPromptAsync(templateType, entity, context);
        
        var campaign = entity as Campaign;
        var product = entity as Product;

        if (campaign != null)
        {
            var campaignTypeText = campaign.Type.ToString();
            var conscienceLevelText = campaign.ConscienceLevel.ToString();
            var sophisticationLevelText = campaign.SophisticationLevel.ToString();

            var deliverablesText = product?.Deliverables.ToString();
            var customerJourneyText = product?.CustomerJourney.ToString();
            
            var prompt = $"""
                              {analysisPrompt}

                              # GERAÇÃO DE CONTEÚDO SOLICITADO

                              Com base na análise do setup acima, gere agora o seguinte conteúdo:

                              **SOLICITAÇÃO**: {contentRequest}

                              **INSTRUÇÕES**:
                              - Use as informações do setup para criar conteúdo personalizado
                              - Mantenha consistência com o tom de voz identificado
                              - Incorpore os elementos únicos (autoridade, mecanismo, valores)
                              - Foque na conversão e engagement

                              Gere o conteúdo solicitado de forma direta e aplicável.

                              Campaign Type: {campaignTypeText}
                              Conscience Level: {conscienceLevelText}
                              Sophistication Level: {sophisticationLevelText}
                              Deliverables: {deliverablesText}
                              Customer Journey: {customerJourneyText}
                          """;

            return await aiOrchestrator.GenerateContentAsync(prompt);
        }
        else if (product != null)
        {
            var deliverablesText = product.Deliverables.ToString();
            var customerJourneyText = product.CustomerJourney.ToString();
            
            var prompt = $"""
                              {analysisPrompt}

                              # GERAÇÃO DE CONTEÚDO SOLICITADO

                              Com base na análise do setup acima, gere agora o seguinte conteúdo:

                              **SOLICITAÇÃO**: {contentRequest}

                              **INSTRUÇÕES**:
                              - Use as informações do setup para criar conteúdo personalizado
                              - Mantenha consistência com o tom de voz identificado
                              - Incorpore os elementos únicos (benefícios, diferenciais, provas)
                              - Foque na conversão e engagement

                              Gere o conteúdo solicitado de forma direta e aplicável.

                              Deliverables: {deliverablesText}
                              Customer Journey: {customerJourneyText}
                          """;

            return await aiOrchestrator.GenerateContentAsync(prompt);
        }
        else
        {
            var prompt = $"""
                              {analysisPrompt}

                              # GERAÇÃO DE CONTEÚDO SOLICITADO

                              Com base na análise do setup acima, gere agora o seguinte conteúdo:

                              **SOLICITAÇÃO**: {contentRequest}

                              **INSTRUÇÕES**:
                              - Use as informações do setup para criar conteúdo personalizado
                              - Mantenha consistência com o tom de voz identificado
                              - Incorpore os elementos únicos relevantes
                              - Foque na conversão e engagement

                              Gere o conteúdo solicitado de forma direta e aplicável.
                          """;

            return await aiOrchestrator.GenerateContentAsync(prompt);
        }
    }
}
