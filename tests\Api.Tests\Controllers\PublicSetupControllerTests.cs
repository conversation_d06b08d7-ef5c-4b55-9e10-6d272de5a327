using Api.Controllers;
using Api.Models;
using Domain.Entities;
using Domain.Interfaces.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Api.Tests.Controllers;

public class PublicSetupControllerTests
{
    private readonly Mock<IPublicService> _mockService;
    private readonly Mock<ILogger<PublicSetupController>> _mockLogger;
    private readonly PublicSetupController _controller;

    public PublicSetupControllerTests()
    {
        _mockService = new Mock<IPublicService>();
        _mockLogger = new Mock<ILogger<PublicSetupController>>();
        _controller = new PublicSetupController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnOk_WhenSuccess()
    {
        var id = "pub-1";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ReturnsAsync("TEMPLATE");

        var result = await _controller.GenerateSetupTemplate(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        var payload = ok.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeTrue();
        payload.EntityType.Should().Be("Public");
        payload.Identifier.Should().Be(id);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new KeyNotFoundException("not found"));

        var result = await _controller.GenerateSetupTemplate(id);

        var nf = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var payload = nf.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
        payload.Message.Should().Be("not found");
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GenerateSetupTemplate(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GetPublic_ShouldReturnOk_WhenFound()
    {
        var id = "pub-1";
        var entity = new Public 
        { 
            Id = 1, 
            Name = "Audience",
            Local = "City",
            FamilySituation = "Single",
            Personality = "Calm",
            Hobbies = "Reading",
            Lifestyle = "Active",
            PersonalValue = "Honesty",
            Roof = "Roof",
            NextLevel = "Next",
            DropOfWater = "Drop",
            Beliefs = "Beliefs",
            SelfVisions = "Vision",
            PossibleObjections = "Objections",
            OwnCommunication = "Tone"
        };
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id)).ReturnsAsync(entity);

        var result = await _controller.GetPublic(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        ok.Value.Should().Be(entity);
    }

    [Fact]
    public async Task GetPublic_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new KeyNotFoundException());

        var result = await _controller.GetPublic(id);

        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetPublic_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GetPublic(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }
}


