using Api.Controllers;
using Api.Models;
using Domain.Entities;
using Domain.Interfaces.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Api.Tests.Controllers;

public class ExpertSetupControllerTests
{
    private readonly Mock<IExpertService> _mockService;
    private readonly Mock<ILogger<ExpertSetupController>> _mockLogger;
    private readonly ExpertSetupController _controller;

    public ExpertSetupControllerTests()
    {
        _mockService = new Mock<IExpertService>();
        _mockLogger = new Mock<ILogger<ExpertSetupController>>();
        _controller = new ExpertSetupController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnOk_WhenSuccess()
    {
        var identifier = "exp-1";
        var expectedTemplate = "TEMPLATE";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(identifier, It.IsAny<string>()))
            .ReturnsAsync(expectedTemplate);

        var result = await _controller.GenerateSetupTemplate(identifier, "copywriter");

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        var payload = ok.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeTrue();
        payload.Template.Should().Be(expectedTemplate);
        payload.EntityType.Should().Be("Expert");
        payload.Identifier.Should().Be(identifier);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnNotFound_WhenEntityMissing()
    {
        var identifier = "missing";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(identifier, null))
            .ThrowsAsync(new KeyNotFoundException("not found"));

        var result = await _controller.GenerateSetupTemplate(identifier, null);

        var notFound = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var payload = notFound.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
        payload.Message.Should().Be("not found");
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturn500_OnUnhandledError()
    {
        var identifier = "exp-err";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(identifier, null))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GenerateSetupTemplate(identifier, null);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
        var payload = obj.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
    }

    [Fact]
    public async Task GetExpert_ShouldReturnOk_WhenFound()
    {
        var identifier = "exp-1";
        var expert = new Expert 
        { 
            Id = 1, 
            Name = "John",
            AreaOfExpertise = "Copy",
            Biography = "Bio",
            Moment = "Now",
            Dolor = "Pain",
            Credibility = "High",
            Recognition = "Awards",
            TrackRecord = "Track",
            HowProductWorks = "Explained",
            VoicePersonality = "Friendly",
            EssentialValues = "Values",
            Enterprise = "Company"
        };
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(identifier))
            .ReturnsAsync(expert);

        var result = await _controller.GetExpert(identifier);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        ok.Value.Should().Be(expert);
    }

    [Fact]
    public async Task GetExpert_ShouldReturnNotFound_WhenMissing()
    {
        var identifier = "missing";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(identifier))
            .ThrowsAsync(new KeyNotFoundException("missing"));

        var result = await _controller.GetExpert(identifier);

        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetExpert_ShouldReturn500_OnUnhandledError()
    {
        var identifier = "err";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(identifier))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GetExpert(identifier);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }
}


