using Domain.Entities;
using Scriban;

namespace Application.Prompts.Setups;

public static class ProductSetup
{
    private static readonly Template _productTemplate;
    
    static ProductSetup()
    {
        var assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
        var assemblyDir = Path.GetDirectoryName(assemblyPath);
        var templatePath = Path.Combine(assemblyDir!, "Templates", "product-setup.sbn");
        
        if (!File.Exists(templatePath))
        {
            var currentDir = Directory.GetCurrentDirectory();
            templatePath = Path.Combine(currentDir, "src", "Application", "Templates", "product-setup.sbn");
        }
        
        var templateContent = File.ReadAllText(templatePath);
        _productTemplate = Template.Parse(templateContent);
    }
    
    public static string ProductSetupPrompt(Product product)
    {
        var model = new
        {
            product = new
            {
                name = product.Name,
                benefits = product.Benefits,
                social_proof = product.SocialProof,
                metodology = product.Metodology,
                guarantee = product.Guarantee,
                deliverables = product.Deliverables.ToString(),
                customer_journey = product.CustomerJourney.ToString()
            }
        };
        
        return _productTemplate.Render(model);
    }
}
