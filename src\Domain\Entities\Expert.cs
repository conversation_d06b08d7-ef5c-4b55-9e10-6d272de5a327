namespace Domain.Entities;

using Domain.Interfaces;
using Google.Cloud.Firestore;

[FirestoreData]
public sealed class Expert : IEntity
{
    [FirestoreProperty]
    public int Id { get; set; }
    
    [FirestoreProperty]
    public string ExternalId { get; set; } = string.Empty;
    
    [FirestoreProperty]
    public required string Name { get; set; }
    
    [FirestoreProperty]
    public required string AreaOfExpertise { get; set; }
    
    [FirestoreProperty]
    public required string Biography { get; set; }

    [FirestoreProperty]
    public required string Moment { get; set; }

    [FirestoreProperty]
    public required string Dolor { get; set; }
    
    [FirestoreProperty]
    public required string Credibility { get; set; }

    [FirestoreProperty]
    public required string Recognition { get; set; }

    [FirestoreProperty]
    public required string TrackRecord { get; set; }
    
    [FirestoreProperty]
    public required string HowProductWorks { get; set; }

    [FirestoreProperty]
    public required string VoicePersonality { get; set; }

    [FirestoreProperty]
    public required string EssentialValues { get; set; }

    [FirestoreProperty]
    public required string Enterprise { get; set; }
}