# Como Usar Templates Scriban como Prompts para IA

## 📋 **Funcionalidades Implementadas**

A implementação permite transformar seus templates Scriban existentes em prompts inteligentes para IA, oferecendo 4 tipos de análise:

### 1. **Análise de Setup** 
Analisa a completude e qualidade dos dados do setup baseado no template.

### 2. **Sugestões de Melhoria**
Gera recomendações específicas para otimizar o setup.

### 3. **Validação de Completude**
Verifica se todos os campos essenciais estão preenchidos.

### 4. **Geração de Conteúdo**
Cria conteúdo específico (copy, emails, posts) baseado no setup processado.

## 🚀 **Como Usar**

### **Exemplo 1: Analisar Setup de Expert**

```bash
POST /api/content/analyze-setup/expert
Content-Type: application/json

{
  "name": "<PERSON>. <PERSON>",
  "areaOfExpertise": "Nutrição Funcional",
  "biography": "25 anos tratando diabetes com alimentação...",
  "dolor": "Perdeu a mãe por diabetes mal controlada",
  "credibility": "PhD em Nutrição, 10.000+ pacientes tratados",
  "voicePersonality": "Empático, direto, baseado em ciência"
}
```

**Resposta:**
```json
{
  "success": true,
  "analysis": "ANÁLISE DO SETUP DE EXPERT:\n\n✅ PONTOS FORTES:\n- História pessoal forte com conexão emocional\n- Credenciais sólidas...\n\n⚠️ LACUNAS:\n- Falta definir mecanismo único\n- Tom de voz pode ser mais específico...",
  "templateType": "expert"
}
```

### **Exemplo 2: Gerar Sugestões de Melhoria**

```bash
POST /api/content/improve-setup/expert
Content-Type: application/json

{
  "name": "Dr. João Silva",
  "areaOfExpertise": "Nutrição Funcional"
  // ... demais campos
}
```

### **Exemplo 3: Gerar Conteúdo a partir do Template**

```bash
POST /api/content/generate-from-template/expert
Content-Type: application/json

{
  "entity": {
    "name": "Dr. João Silva",
    "areaOfExpertise": "Nutrição Funcional",
    // ... setup completo
  },
  "contentRequest": "Escreva um email de lançamento de 300 palavras para o novo curso",
  "context": "Público: pessoas com diabetes tipo 2",
  "provider": "chatgpt"
}
```

**Resposta:**
```json
{
  "success": true,
  "generatedContent": "Assunto: A Descoberta que Salvou Minha Mãe (e Pode Salvar Você)\n\nOlá [Nome],\n\nEu sou Dr. João Silva, e preciso compartilhar algo pessoal...\n\n[Conteúdo personalizado baseado no setup]",
  "templateType": "expert",
  "contentRequest": "email de lançamento"
}
```

## 🎯 **Templates Suportados**

- `expert` - Para especialistas e empresas
- `campaign` - Para campanhas de marketing
- `product` - Para produtos e serviços
- `public` - Para definição de público-alvo

## 💡 **Casos de Uso Práticos**

### **Para Copywriters:**
1. **Setup Audit**: Validate se o briefing está completo
2. **Ideação**: Gere variações de headlines baseadas no setup
3. **Consistency Check**: Verifique se o tom está alinhado

### **Para Agências:**
1. **Client Onboarding**: Valide informações do cliente
2. **Content Planning**: Gere calendário editorial baseado no setup
3. **Quality Assurance**: Analise setups antes da produção

### **Para Especialistas:**
1. **Self-Assessment**: Analise seu próprio posicionamento
2. **Content Ideas**: Gere ideias de conteúdo personalizadas
3. **Messaging Optimization**: Refine sua comunicação

## 🔧 **Integração com Existing Services**

A implementação se integra perfeitamente com sua arquitetura existente:

- **Templates Scriban** já existentes são reutilizados
- **AI Orchestrator** continua gerenciando provedores (ChatGPT/Gemini)  
- **Services** mantêm responsabilidades bem definidas
- **Clean Architecture** é preservada

## 📊 **Benefícios Alcançados**

✅ **Automatização**: Templates Scriban viram prompts inteligentes automaticamente
✅ **Consistência**: Mesma estrutura de briefing para análise e geração
✅ **Escalabilidade**: Funciona com qualquer template novo que você criar
✅ **Flexibilidade**: Suporta qualquer tipo de conteúdo solicitado
✅ **Qualidade**: IA tem contexto completo do setup para gerar conteúdo relevante

## 🎨 **Próximos Passos Sugeridos**

1. **Teste os endpoints** com seus setups existentes
2. **Refine os templates** para melhorar a qualidade dos prompts
3. **Crie workflows** específicos para diferentes tipos de campanha
4. **Monitore resultados** e ajuste prompts conforme necessário
