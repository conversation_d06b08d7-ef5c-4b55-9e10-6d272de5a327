using Application.Services.AI;
using Domain.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Moq;
using Xunit;
using FluentAssertions;
using Api.Controllers;
using System.Text;

namespace Api.Tests.Controllers;

public class ContentControllerTests
{
    private readonly Mock<IAiOrchestrator> _mockAiOrchestrator;
    private readonly Mock<IFileReaderService> _mockFileReaderService;
    private readonly Mock<ILogger<ContentController>> _mockLogger;
    private readonly ContentController _controller;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public ContentControllerTests()
    {
        _mockAiOrchestrator = new Mock<IAiOrchestrator>();
        _mockFileReaderService = new Mock<IFileReaderService>();
        _mockLogger = new Mock<ILogger<ContentController>>();
        _mockConfiguration = new Mock<IConfiguration>();
        
        _controller = new ContentController(_mockAiOrchestrator.Object, _mockFileReaderService.Object, _mockLogger.Object, _mockConfiguration.Object);
    }

    [Fact]
    public async Task GenerateContent_WithValidRequest_ShouldReturnOk()
    {
        var request = new GenerateContentRequest { Prompt = "Test prompt" };
        var expectedResponse = "AI generated content";
        
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(request.Prompt, request.Provider))
            .ReturnsAsync(expectedResponse);

        var result = await _controller.GenerateContent(request);

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<GenerateContentResponse>().Subject;
        response.Success.Should().BeTrue();
        response.Content.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task GenerateContent_WithEmptyPrompt_ShouldReturnBadRequest()
    {
        var request = new GenerateContentRequest { Prompt = "" };
        
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new ArgumentException("Prompt cannot be empty"));

        var result = await _controller.GenerateContent(request);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<GenerateContentResponse>().Subject;
        response.Success.Should().BeFalse();
    }

    [Fact]
    public async Task AnalyzeFile_WithValidFile_ShouldReturnOk()
    {
        var fileName = "test.txt";
        var fileContent = "Test file content";
        var aiAnalysis = "This is a text file containing test data";
        
        var mockFile = CreateMockFile(fileName, fileContent);
        
        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(true);
        _mockFileReaderService.Setup(x => x.ReadFileAsync(It.IsAny<Stream>(), fileName))
            .ReturnsAsync(fileContent);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(aiAnalysis);

        var result = await _controller.AnalyzeFile(mockFile, null);

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<FileAnalysisResponse>().Subject;
        response.Success.Should().BeTrue();
        response.FileName.Should().Be(fileName);
        response.Analysis.Should().Be(aiAnalysis);
    }

    [Fact]
    public async Task AnalyzeFile_WithNullFile_ShouldReturnBadRequest()
    {
        var result = await _controller.AnalyzeFile(null, null);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<FileAnalysisResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Be("No file was uploaded");
    }

    [Fact]
    public async Task AnalyzeFile_WithUnsupportedFileType_ShouldReturnBadRequest()
    {
        var fileName = "test.exe";
        var mockFile = CreateMockFile(fileName, "binary content");
        
        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(false);
        _mockFileReaderService.Setup(x => x.GetSupportedExtensions())
            .Returns(new[] { ".txt", ".pdf", ".csv" });

        var result = await _controller.AnalyzeFile(mockFile, null);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<FileAnalysisResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Contain("File type not supported");
    }

    [Fact]
    public async Task AnalyzeFile_WithEmptyFile_ShouldReturnBadRequest()
    {
        var fileName = "empty.txt";
        var mockFile = CreateMockFile(fileName, "");
        
        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(true);
        _mockFileReaderService.Setup(x => x.ReadFileAsync(It.IsAny<Stream>(), fileName))
            .ReturnsAsync("");

        var result = await _controller.AnalyzeFile(mockFile, null);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<FileAnalysisResponse>().Subject;
        response.Success.Should().BeFalse();
        response.Message.Should().Be("The file is empty or could not be read");
    }

    [Fact]
    public async Task AnalyzeFile_WithFileReadError_ShouldReturnBadRequest()
    {
        var fileName = "test.txt";
        var mockFile = CreateMockFile(fileName, "content");
        
        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(true);
        _mockFileReaderService.Setup(x => x.ReadFileAsync(It.IsAny<Stream>(), fileName))
            .ThrowsAsync(new NotSupportedException("File type not supported"));

        var result = await _controller.AnalyzeFile(mockFile, null);

        var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var response = badRequestResult.Value.Should().BeOfType<FileAnalysisResponse>().Subject;
        response.Success.Should().BeFalse();
    }

    [Fact]
    public async Task AnalyzeFile_WithAiServiceError_ShouldReturnInternalServerError()
    {
        var fileName = "test.txt";
        var fileContent = "Test content";
        var mockFile = CreateMockFile(fileName, fileContent);
        
        _mockFileReaderService.Setup(x => x.IsFileTypeSupported(fileName))
            .Returns(true);
        _mockFileReaderService.Setup(x => x.ReadFileAsync(It.IsAny<Stream>(), fileName))
            .ReturnsAsync(fileContent);
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync(It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new InvalidOperationException("AI service unavailable"));

        var result = await _controller.AnalyzeFile(mockFile, null);

        var serverErrorResult = result.Should().BeOfType<ObjectResult>().Subject;
        serverErrorResult.StatusCode.Should().Be(500);
        var response = serverErrorResult.Value.Should().BeOfType<FileAnalysisResponse>().Subject;
        response.Success.Should().BeFalse();
    }

    [Fact]
    public void GetAvailableProviders_ShouldReturnProvidersList()
    {
        var result = _controller.GetAvailableProviders();

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var providers = okResult.Value.Should().BeAssignableTo<object[]>().Subject;
        providers.Should().HaveCount(2);
    }

    [Fact]
    public async Task CheckHealth_ShouldReturnHealthStatus()
    {
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync("Responda apenas 'OK'", "chatgpt"))
            .ReturnsAsync("OK");
        _mockAiOrchestrator.Setup(x => x.GenerateContentAsync("Responda apenas 'OK'", "gemini"))
            .ReturnsAsync("OK");

        var result = await _controller.CheckHealth();

        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        okResult.Value.Should().NotBeNull();
    }

    private static IFormFile CreateMockFile(string fileName, string content)
    {
        var bytes = Encoding.UTF8.GetBytes(content);
        var stream = new MemoryStream(bytes);
        
        var mockFile = new Mock<IFormFile>();
        mockFile.Setup(f => f.FileName).Returns(fileName);
        mockFile.Setup(f => f.Length).Returns(bytes.Length);
        mockFile.Setup(f => f.OpenReadStream()).Returns(stream);
        
        return mockFile.Object;
    }
}
