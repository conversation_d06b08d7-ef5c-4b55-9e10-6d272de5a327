using Microsoft.Extensions.DependencyInjection;
using Application.Services;
using Domain.Interfaces;
using System.Text;
using Xunit;

namespace Application.Tests.Services;

public class FileReaderServiceTests
{
    private readonly IFileReaderService _fileReaderService;

    public FileReaderServiceTests()
    {
        _fileReaderService = new FileReaderService();
    }

    [Fact]
    public async Task ReadFileAsync_WithTextFile_ShouldReturnContent()
    {
        var tempFile = Path.ChangeExtension(Path.GetTempFileName(), ".txt");
        var expectedContent = "Este é um teste de arquivo de texto.";
        await File.WriteAllTextAsync(tempFile, expectedContent);

        try
        {
            var result = await _fileReaderService.ReadFileAsync(tempFile);
            Assert.Equal(expectedContent, result);
        }
        finally
        {
            File.Delete(tempFile);
        }
    }

    [Fact]
    public async Task ReadFileAsync_WithStream_ShouldReturnContent()
    {
        var content = "Conteúdo de teste via stream";
        var bytes = Encoding.UTF8.GetBytes(content);
        using var stream = new MemoryStream(bytes);

        var result = await _fileReaderService.ReadFileAsync(stream, "test.txt");

        Assert.Equal(content, result);
    }

    [Fact]
    public void IsFileTypeSupported_WithSupportedExtension_ShouldReturnTrue()
    {
        Assert.True(_fileReaderService.IsFileTypeSupported("test.pdf"));
        Assert.True(_fileReaderService.IsFileTypeSupported("test.csv"));
        Assert.True(_fileReaderService.IsFileTypeSupported("test.docx"));
        Assert.True(_fileReaderService.IsFileTypeSupported("test.xlsx"));
    }

    [Fact]
    public void IsFileTypeSupported_WithUnsupportedExtension_ShouldReturnFalse()
    {
        Assert.False(_fileReaderService.IsFileTypeSupported("test.exe"));
        Assert.False(_fileReaderService.IsFileTypeSupported("test.bin"));
    }

    [Fact]
    public async Task ReadFileAsync_WithNonExistentFile_ShouldThrowException()
    {
        await Assert.ThrowsAsync<FileNotFoundException>(() => 
            _fileReaderService.ReadFileAsync("arquivo_inexistente.pdf"));
    }

    [Fact]
    public void GetSupportedExtensions_ShouldReturnAllSupportedTypes()
    {
        var extensions = _fileReaderService.GetSupportedExtensions().ToList();

        Assert.Contains(".pdf", extensions);
        Assert.Contains(".csv", extensions);
        Assert.Contains(".txt", extensions);
        Assert.Contains(".docx", extensions);
        Assert.Contains(".xlsx", extensions);
        Assert.True(extensions.Count > 5);
    }
}
