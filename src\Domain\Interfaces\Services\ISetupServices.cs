namespace Domain.Interfaces.Services;

public interface IExpertService
{
    Task<string> GenerateSetupTemplateAsync(string identifier, string? expertType = null);
    Task<Domain.Entities.Expert> GetByIdOrExternalIdAsync(string identifier);
    Task<Domain.Entities.Expert> CreateExpertAsync(Domain.Entities.Expert expert);
}

public interface ICampaignService
{
    Task<string> GenerateSetupTemplateAsync(string identifier, string? expertId = null, string? productId = null, string? publicId = null);
    Task<Domain.Entities.Campaign> GetByIdOrExternalIdAsync(string identifier);
    Task<Domain.Entities.Campaign> CreateCampaignAsync(Domain.Entities.Campaign campaign);
}

public interface IProductService
{
    Task<string> GenerateSetupTemplateAsync(string identifier);
    Task<Domain.Entities.Product> GetByIdOrExternalIdAsync(string identifier);
    Task<Domain.Entities.Product> CreateProductAsync(Domain.Entities.Product product);
}

public interface IPublicService
{
    Task<string> GenerateSetupTemplateAsync(string identifier);
    Task<Domain.Entities.Public> GetByIdOrExternalIdAsync(string identifier);
    Task<Domain.Entities.Public> CreatePublicAsync(Domain.Entities.Public publicEntity);
}