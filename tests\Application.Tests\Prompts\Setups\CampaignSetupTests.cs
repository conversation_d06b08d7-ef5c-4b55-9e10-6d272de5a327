using Application.Prompts.Setups;
using Domain.Entities;
using Domain.Enums.Campaing;
using Domain.Enums.Products;
using Domain.Enums.Public;
using Xunit;

namespace Application.Tests.Prompts.Setups;

public class CampaignSetupTests
{
    [Fact]
    public void CampaignSetupPrompt_ShouldRenderTemplateWithEntityData()
    {
        // Arrange
        var campaign = new Campaign
        {
            Name = "Test Campaign",
            Type = CampaingTypeEnum.WebinarOurEvent,
            ConscienceLevel = CampaingConscienceLevelEnum.AwareOfProblem,
            SophisticationLevel = CampaingSophisticationLevelEnum.FirstPresent,
            Ideia = "Test campaign idea",
            Emotion = "Curiosity",
            Belief = "Test belief"
        };

        var product = new Product
        {
            Name = "Test Product",
            Benefits = "Amazing benefits",
            SocialProof = "Great testimonials",
            Metodology = "Proven methodology",
            Guarantee = "30-day guarantee",
            Deliverables = ProductDeliverablesEnum.SessionMentoring,
            CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
        };

        var expert = new Expert
        {
            Name = "John Doe",
            AreaOfExpertise = "Digital Marketing",
            Biography = "Expert biography",
            Moment = "Present moment",
            Dolor = "Pain point expert addresses",
            Credibility = "High credibility",
            Recognition = "Industry recognition",
            TrackRecord = "Proven track record",
            HowProductWorks = "Product functionality",
            VoicePersonality = "Professional and friendly",
            EssentialValues = "Integrity and excellence",
            Enterprise = "Expert Company LLC"
        };

        var publicTarget = new Public
        {
            Name = "Target Audience",
            Local = "Global",
            FamilySituation = "Mixed demographics",
            Personality = "Ambitious professionals",
            Hobbies = "Professional development",
            Lifestyle = "Career-focused",
            PersonalValue = "Success and growth",
            Roof = "Current limitations",
            NextLevel = "Desired future state",
            DropOfWater = "Final trigger point",
            Beliefs = "Hard work pays off",
            SelfVisions = "Successful professionals",
            PossibleObjections = "Time constraints",
            OwnCommunication = "Direct and clear"
        };

        // Act
        var result = CampaignSetup.CampaignSetupPrompt(campaign, product, expert, publicTarget);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.Contains("WebinarOurEvent", result);
        Assert.Contains("Test Product", result);
        Assert.Contains("John Doe", result);
        Assert.Contains("Target Audience", result);
    }
}
