using Application.Prompts.Setups;
using Domain.Entities;
using Xunit;

namespace Application.Tests.Prompts.Setups;

public class PublicSetupTests
{
    [Fact]
    public void PublicSetupPrompt_ShouldRenderTemplateWithPublicData()
    {
        // Arrange
        var publicTarget = new Public
        {
            Name = "<PERSON> Empresário - Avatar Principal",
            Local = "São Paulo, Brasil - Grandes centros urbanos, foco em regiões metropolitanas",
            FamilySituation = "Casado, 2 filhos adolescentes, cônjuge também trabalha em área corporativa",
            Personality = "Ambicioso, busca ser reconhecido como líder inovador no seu setor, perfeccionista",
            Hobbies = "Investimentos em ações e fundos, tecnologia e gadgets, golfe nos fins de semana, viagens internacionais a negócios",
            Lifestyle = "Trabalha 10-12h/dia, valoriza eficiência máxima, early adopter de tecnologias, busca otimização constante",
            PersonalValue = "Segurança financeira da família, crescimento profissional contínuo, status social, liberdade de tempo",
            Roof = "Atingiu o teto como operador - agenda lotada de reuniões virou limitação do próprio faturamento e crescimento",
            NextLevel = "Quer ir de operador para arquiteto estratégico, construir um negócio que funcione sem sua presença constante",
            DropOfWater = "Sente-se prisioneiro do próprio sucesso - criou um emprego caro ao invés de um negócio de verdade, trabalha MAIS que quando era funcionário",
            Beliefs = "Consultorias são caras e teóricas demais, é difícil encontrar soluções práticas e implementáveis, a maioria dos gurus nunca tocou um negócio real",
            SelfVisions = "Se vê como alguém esperto e bem-sucedido, mas internamente sente que está perdendo o controle do próprio tempo e vida pessoal",
            PossibleObjections = "É muito caro para o momento, não tenho tempo para implementar mais uma coisa, já tentei consultoria antes e não deu certo, precisa ser algo que funcione rápido",
            OwnCommunication = "Linguagem direta e objetiva, baseada em resultados mensuráveis, gosta de dados concretos e cases reais, detesta enrolação e teoria sem prática"
        };

        // Act
        var result = PublicSetup.PublicSetupPrompt(publicTarget);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        // Verificar se os dados do público estão no resultado
        Assert.Contains("João Empresário - Avatar Principal", result);
        Assert.Contains("São Paulo, Brasil", result);
        Assert.Contains("Casado, 2 filhos adolescentes", result);
        Assert.Contains("Investimentos em ações e fundos", result);
        Assert.Contains("arquiteto estratégico", result);
        
        // Verificar estrutura do template
        Assert.Contains("BRIEFING DE PÚBLICO-ALVO", result);
        Assert.Contains("DEMOGRAFIA DETALHADA", result);
        Assert.Contains("PSICOGRAFIA, INTERESSES E ESTILO DE VIDA", result);
        Assert.Contains("O ESTADO DE ESPÍRITO HIGH TICKET", result);
        Assert.Contains("CRENÇAS, OBJEÇÕES E VISÃO DE MUNDO", result);
        Assert.Contains("LINGUAGEM E COMUNICAÇÃO", result);
        
        // Verificar se não há variáveis não renderizadas
        Assert.DoesNotContain("{{", result);
        Assert.DoesNotContain("}}", result);
        
        // Verificar conceitos específicos do framework high ticket
        Assert.Contains("MOMENTO CATALISADOR", result);
        Assert.Contains("DOR DE IDENTIDADE", result);
        Assert.Contains("DOG WHISTLES", result);
        Assert.Contains("PRÓXIMO NÍVEL", result);
    }

    [Fact]
    public void PublicSetupPrompt_ShouldIncludeHighTicketFrameworkElements()
    {
        // Arrange
        var publicTarget = new Public
        {
            Name = "Maria Executiva",
            Local = "Rio de Janeiro",
            FamilySituation = "Solteira, foco na carreira",
            Personality = "Determinada, competitiva",
            Hobbies = "Networking, leitura de business",
            Lifestyle = "Workaholic, busca eficiência",
            PersonalValue = "Reconhecimento profissional",
            Roof = "Chegou ao limite da estrutura atual",
            NextLevel = "Quer ser referência no mercado",
            DropOfWater = "Sente que trabalha muito mas não evolui",
            Beliefs = "Resultados vêm com esforço",
            SelfVisions = "Se vê como líder natural",
            PossibleObjections = "Falta de tempo para novos projetos",
            OwnCommunication = "Comunicação assertiva e dados"
        };

        // Act
        var result = PublicSetup.PublicSetupPrompt(publicTarget);

        // Assert
        // Verificar elementos específicos do framework high ticket
        Assert.Contains("Momento de Transição (O Teto)", result);
        Assert.Contains("Momento de Dor Aguda (A Gota D'Água)", result);
        Assert.Contains("Momento de Aspiração Elevada", result);
        Assert.Contains("O BISTURI DA DOR", result);
        Assert.Contains("Hierarquia da Dor Sofisticada", result);
        Assert.Contains("DOG WHISTLES E JARGÕES", result);
        Assert.Contains("O Filtro Magnético", result);
        Assert.Contains("JORNADA DE COMPRA HIGH TICKET", result);
        Assert.Contains("FRAMEWORK DE SEGMENTAÇÃO HIGH TICKET", result);
    }

    [Fact]
    public void PublicSetupPrompt_ShouldMapAllEntityPropertiesCorrectly()
    {
        // Arrange
        var publicTarget = new Public
        {
            Name = "TestName",
            Local = "TestLocal", 
            FamilySituation = "TestFamily",
            Personality = "TestPersonality",
            Hobbies = "TestHobbies",
            Lifestyle = "TestLifestyle",
            PersonalValue = "TestValues",
            Roof = "TestRoof",
            NextLevel = "TestNextLevel",
            DropOfWater = "TestDropOfWater",
            Beliefs = "TestBeliefs",
            SelfVisions = "TestSelfVisions",
            PossibleObjections = "TestObjections",
            OwnCommunication = "TestCommunication"
        };

        // Act
        var result = PublicSetup.PublicSetupPrompt(publicTarget);

        // Assert
        // Verificar se todas as propriedades da entidade estão sendo mapeadas
        Assert.Contains("TestName", result);
        Assert.Contains("TestLocal", result);
        Assert.Contains("TestFamily", result);
        Assert.Contains("TestPersonality", result);
        Assert.Contains("TestHobbies", result);
        Assert.Contains("TestLifestyle", result);
        Assert.Contains("TestValues", result);
        Assert.Contains("TestRoof", result);
        Assert.Contains("TestNextLevel", result);
        Assert.Contains("TestDropOfWater", result);
        Assert.Contains("TestBeliefs", result);
        Assert.Contains("TestSelfVisions", result);
        Assert.Contains("TestObjections", result);
        Assert.Contains("TestCommunication", result);
    }

    [Fact]
    public void PublicSetupPrompt_ShouldIncludeValidationChecklist()
    {
        // Arrange
        var publicTarget = new Public
        {
            Name = "Avatar Teste",
            Local = "Brasil",
            FamilySituation = "Variado",
            Personality = "Profissional",
            Hobbies = "Desenvolvimento",
            Lifestyle = "Corporativo",
            PersonalValue = "Crescimento",
            Roof = "Limitações atuais",
            NextLevel = "Próximo patamar",
            DropOfWater = "Gatilho final",
            Beliefs = "Crenças centrais",
            SelfVisions = "Autoimagem",
            PossibleObjections = "Resistências",
            OwnCommunication = "Estilo comunicativo"
        };

        // Act
        var result = PublicSetup.PublicSetupPrompt(publicTarget);

        // Assert
        // Verificar se inclui elementos de validação e próximos passos
        Assert.Contains("VALIDAÇÃO DO AVATAR", result);
        Assert.Contains("PRÓXIMOS PASSOS", result);
        Assert.Contains("ITENS PARA COMPLEMENTAR", result);
        Assert.Contains("FRAMEWORK DE APLICAÇÃO", result);
        
        // Verificar checklist de validação
        Assert.Contains("Confirmar se o momento catalisador gera urgência real", result);
        Assert.Contains("Validar se a dor de identidade justifica investimento high ticket", result);
        Assert.Contains("Testar se a linguagem ressoa com o público-alvo real", result);
    }
}
