using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Api.DTOs;

namespace Api.Controllers;

[ApiController]
[Route("api/expert-setup")]
public class ExpertSetupController(IExpertService expertService, ILogger<ExpertSetupController> logger) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateExpert([FromBody] CreateExpertDto createExpertDto)
    {
        try
        {
            var expert = new Domain.Entities.Expert
            {
                Name = createExpertDto.Name,
                AreaOfExpertise = createExpertDto.AreaOfExpertise,
                Biography = createExpertDto.Biography,
                Moment = createExpertDto.Moment,
                Dolor = createExpertDto.Dolor,
                Credibility = createExpertDto.Credibility,
                Recognition = createExpertDto.Recognition,
                TrackRecord = createExpertDto.TrackRecord,
                HowProductWorks = createExpertDto.HowProductWorks,
                VoicePersonality = createExpertDto.VoicePersonality,
                EssentialValues = createExpertDto.EssentialValues,
                Enterprise = createExpertDto.Enterprise
            };

            var createdExpert = await expertService.CreateExpertAsync(expert);
            return CreatedAtAction(nameof(GetExpert), new { identifier = createdExpert.ExternalId }, createdExpert);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while creating the expert.");
            return StatusCode(500, new { message = "An error occurred while creating the expert.", details = ex.Message });
        }
    }

    [HttpGet("{identifier}/template")]
    public async Task<IActionResult> GenerateSetupTemplate(string identifier, [FromQuery] string? expertType = null)
    {
        try
        {
            logger.LogInformation("Generating expert setup template for identifier: {Identifier}", identifier);
            
            var template = await expertService.GenerateSetupTemplateAsync(identifier, expertType);
            
            return Ok(new SetupTemplateResponse
            {
                Success = true,
                Template = template,
                Message = "Expert setup template generated successfully",
                EntityType = "Expert",
                Identifier = identifier
            });
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "Expert not found with identifier: {Identifier}", identifier);
            return NotFound(new SetupTemplateResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating expert setup template for identifier: {Identifier}", identifier);
            return StatusCode(500, new SetupTemplateResponse
            {
                Success = false,
                Message = "Internal server error while generating template"
            });
        }
    }

    [HttpGet("{identifier}")]
    public async Task<IActionResult> GetExpert(string identifier)
    {
        try
        {
            var expert = await expertService.GetByIdOrExternalIdAsync(identifier);
            return Ok(expert);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { Message = ex.Message });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving expert with identifier: {Identifier}", identifier);
            return StatusCode(500, new { Message = "Internal server error" });
        }
    }
}