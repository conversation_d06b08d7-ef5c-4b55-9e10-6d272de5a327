<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Api</RootNamespace>
    <AssemblyName>Api</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.8" />
    <PackageReference Include="Scalar.AspNetCore" Version="2.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj">
      <Private>true</Private>
      <CopyLocal>true</CopyLocal>
    </ProjectReference>
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj">
      <Private>true</Private>
      <CopyLocal>true</CopyLocal>
    </ProjectReference>
    <ProjectReference Include="..\Application\Application.csproj">
      <Private>true</Private>
      <CopyLocal>true</CopyLocal>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
      <IncludeAssets>all</IncludeAssets>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <None Update="Templates\*.sbn">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
