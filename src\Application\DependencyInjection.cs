using Application.Services.AI;
using Application.Services;
using Application.Services.Setups;
using Domain.Interfaces;
using Domain.Interfaces.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // HTTP Clients
        services.AddHttpClient<ChatGptService>();
        
        // AI Services
        services.AddTransient<ChatGptService>();
        services.AddTransient<GeminiService>();
        services.AddSingleton<IAiServiceFactory, AiServiceFactory>();
        services.AddScoped<IAiOrchestrator, AiOrchestrator>();
        
        // Application Services
        services.AddScoped<IFileReaderService, FileReaderService>();
        services.AddScoped<ITemplateService, TemplateService>();
        services.AddScoped<IContentService, ContentService>();
        services.AddScoped<IIdGenerationService, IdGenerationService>();
        services.AddScoped<IPromptGenerationService, PromptGenerationService>();
        services.AddScoped<ICompleteSetupService, CompleteSetupService>();
        
        // Setup Services (Domain Services)
        services.AddScoped<IExpertService, ExpertService>();
        services.AddScoped<ICampaignService, CampaignService>();
        services.AddScoped<IProductService, ProductService>();
        services.AddScoped<IPublicService, PublicService>();
        
        return services;
    }
}
