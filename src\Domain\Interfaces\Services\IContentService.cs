using Microsoft.AspNetCore.Http;

namespace Domain.Interfaces.Services;

public interface IContentService
{
    Task<string> GenerateContentAsync(string prompt, string? preferredProvider = null);
    Task<string> AnalyzeFileAsync(IFormFile file);
    Task<string> AnalyzeSetupAsync(string templateType, object entity, string? context = null);
    Task<string> GenerateImprovementSuggestionsAsync(string templateType, object entity);
    Task<string> ValidateSetupCompletenessAsync(string templateType, object entity);
    Task<string> GenerateContentFromTemplateAsync(string templateType, object entity, string contentRequest, string? context = null);
}
