using Domain.Entities;
using Domain.Interfaces.Repositories;
using Domain.Interfaces.Services;

namespace Application.Services.Setups;

public class CampaignService(ICampaignRepository campaignRepository, ITemplateService templateService, 
    IExpertRepository expertRepository, IProductRepository productRepository, IPublicRepository publicRepository, IIdGenerationService idGenerationService) : ICampaignService
{
    public async Task<Campaign> GetByIdAsync(int id)
    {
        return await campaignRepository.GetByIdAsync(id);
    }

    public async Task<Campaign> GetByExternalIdAsync(string externalId)
    {
        return await campaignRepository.GetByExternalIdAsync(externalId);
    }

    public async Task<Campaign> GetByIdOrExternalIdAsync(string identifier)
    {
        if (int.TryParse(identifier, out int id))
        {
            return await campaignRepository.GetByIdAsync(id);
        }
        return await campaignRepository.GetByExternalIdAsync(identifier);
    }

    public async Task<string> GenerateSetupTemplateAsync(string identifier, string? expertId = null, string? productId = null, string? publicId = null)
    {
        var campaign = await GetByIdOrExternalIdAsync(identifier);
        
        Expert? expert = null;
        Product? product = null;
        Public? publicEntity = null;

        if (!string.IsNullOrEmpty(expertId))
        {
            expert = int.TryParse(expertId, out int expId) 
                ? await expertRepository.GetByIdAsync(expId) 
                : await expertRepository.GetByExternalIdAsync(expertId);
        }

        if (!string.IsNullOrEmpty(productId))
        {
            product = int.TryParse(productId, out int prodId) 
                ? await productRepository.GetByIdAsync(prodId) 
                : await productRepository.GetByExternalIdAsync(productId);
        }

        if (!string.IsNullOrEmpty(publicId))
        {
            publicEntity = int.TryParse(publicId, out int pubId) 
                ? await publicRepository.GetByIdAsync(pubId) 
                : await publicRepository.GetByExternalIdAsync(publicId);
        }

        return await templateService.ProcessCampaignTemplateAsync(campaign, expert, product, publicEntity);
    }

    public async Task<IEnumerable<Campaign>> GetAllAsync()
    {
        return await campaignRepository.GetAllAsync();
    }

    public async Task<Campaign> CreateCampaignAsync(Campaign campaign)
    {
        if (string.IsNullOrEmpty(campaign.ExternalId))
        {
            campaign.ExternalId = idGenerationService.GenerateExternalId();
        }
        
        if (campaign.Id == 0)
        {
            campaign.Id = await idGenerationService.GenerateUniqueIdAsync(() => campaignRepository.GetAllAsync());
        }

        await campaignRepository.AddAsync(campaign);
        return campaign;
    }

    public async Task UpdateAsync(Campaign campaign)
    {
        await campaignRepository.UpdateAsync(campaign);
        await campaignRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        await campaignRepository.DeleteAsync(id);
        await campaignRepository.SaveChangesAsync();
    }
}