using Domain.Entities;
using Domain.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace Application.Services;

public class PromptGenerationService(ITemplateService templateService, ILogger<PromptGenerationService> logger)
    : IPromptGenerationService
{
    private readonly string _templatesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");

    public async Task<string> GenerateAnalysisPromptAsync(string templateType, object entity, string? context = null)
    {
        var templateContent = await LoadTemplateAsync(templateType);
        var processedTemplate = await ProcessTemplateWithEntity(templateContent, entity, context);
        
        var analysisPrompt = $@"
        # ANÁLISE DE SETUP DE {templateType.ToUpper()}

        Com base no template de briefing a seguir, analise os dados fornecidos e identifique:

        1. **Pontos Fortes**: O que está bem definido e completo
        2. **Lacunas**: Informações em falta ou incompletas
        3. **Inconsistências**: Dados que podem estar contraditórios
        4. **Oportunidades**: Sugestões para tornar o setup mais efetivo

        ## TEMPLATE DE BRIEFING:
        ```
        {processedTemplate}
        ```

        ## INSTRUÇÃO:
        - Analise cada seção do briefing
        - Identifique campos vazios ou com placeholder (ex: {{{{ variavel }}}})
        - Sugira melhorias específicas
        - Mantenha o foco na efetividade do marketing/copywriting

        Forneça uma análise estruturada e acionável.";

        return analysisPrompt;
    }

    public async Task<string> GenerateImprovementPromptAsync(string templateType, object entity)
    {
        var templateContent = await LoadTemplateAsync(templateType);
        var processedTemplate = await ProcessTemplateWithEntity(templateContent, entity);
        
        var improvementPrompt = $@"
# SUGESTÕES DE MELHORIA PARA {templateType.ToUpper()}

Com base no setup atual, forneça sugestões específicas para:

1. **Aprimorar a Autoridade**: Como fortalecer credibilidade e expertise
2. **Melhorar a Conexão Emocional**: Como criar mais empatia com o público
3. **Clarificar o Mecanismo Único**: Como tornar a proposta mais distintiva
4. **Otimizar a Comunicação**: Como melhorar tom e messaging

## SETUP ATUAL:
```
{processedTemplate}
```

## FORMATO DA RESPOSTA:
Para cada área, forneça:
- Análise do estado atual
- Sugestão específica de melhoria
- Exemplo prático de implementação

Foque em melhorias que impactem diretamente na conversão e engagement.";

        return improvementPrompt;
    }

    public async Task<string> GenerateValidationPromptAsync(string templateType, object entity)
    {
        var templateContent = await LoadTemplateAsync(templateType);
        var processedTemplate = await ProcessTemplateWithEntity(templateContent, entity);
        
        var validationPrompt = $@"
# VALIDAÇÃO DE COMPLETUDE - {templateType.ToUpper()}

Verifique se o setup está completo e pronto para uso em campanhas de marketing.

## CRITÉRIOS DE VALIDAÇÃO:
1. **Completude**: Todos os campos essenciais preenchidos?
2. **Qualidade**: Informações são específicas e detalhadas?
3. **Consistência**: Dados são coerentes entre si?
4. **Aplicabilidade**: Setup permite criar copy efetivo?

## SETUP PARA VALIDAÇÃO:
```
{processedTemplate}
```

## FORMATO DA RESPOSTA:
✅ **APROVADO** / ⚠️ **PENDENTE** / ❌ **REQUER REVISÃO**

### Checklist:
- [ ] Identificação básica completa
- [ ] História/background detalhada
- [ ] Autoridade estabelecida
- [ ] Mecanismo único definido
- [ ] Tom de voz claro
- [ ] Valores essenciais identificados

### Ações Necessárias:
Liste o que precisa ser corrigido/completado antes do uso.";

        return validationPrompt;
    }

    private async Task<string> LoadTemplateAsync(string templateType)
    {
        var templatePath = Path.Combine(_templatesPath, $"{templateType.ToLower()}-setup.sbn");
        
        if (!File.Exists(templatePath))
        {
            throw new FileNotFoundException($"Template not found: {templatePath}");
        }
        
        return await File.ReadAllTextAsync(templatePath);
    }

    private async Task<string> ProcessTemplateWithEntity(string templateContent, object entity, string? context = null)
    {
        try
        {
            return entity switch
            {
                Expert expert => await templateService.ProcessExpertTemplateAsync(expert, context),
                Campaign campaign => await ProcessCampaignTemplate(campaign),
                Product product => await templateService.ProcessProductTemplateAsync(product),
                Public publicEntity => await templateService.ProcessPublicTemplateAsync(publicEntity),
                _ => throw new NotSupportedException($"Entity type {entity.GetType().Name} not supported")
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing template for entity type {EntityType}", entity.GetType().Name);
            return $"Error processing template: {ex.Message}";
        }
    }
    
    private async Task<string> ProcessCampaignTemplate(Campaign campaign)
    {
        return await templateService.ProcessCampaignTemplateAsync(campaign);
    }
}
