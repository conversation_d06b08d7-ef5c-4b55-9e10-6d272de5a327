using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Api.DTOs;

namespace Api.Controllers;

[ApiController]
[Route("api/campaign-setup")]
public class CampaignSetupController(ICampaignService campaignService, ILogger<CampaignSetupController> logger) : ControllerBase
{
    [HttpGet("{identifier}")]
    public async Task<IActionResult> GetCampaign(string identifier)
    {
        try
        {
            var campaign = await campaignService.GetByIdOrExternalIdAsync(identifier);
            return Ok(campaign);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { Message = ex.Message });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving campaign with identifier: {Identifier}", identifier);
            return StatusCode(500, new { Message = "Internal server error" });
        }
    }

    [HttpGet("{identifier}/template")]
    public async Task<IActionResult> GenerateSetupTemplate(string identifier, 
        [FromQuery] string? expertId = null, 
        [FromQuery] string? productId = null, 
        [FromQuery] string? publicId = null)
    {
        try
        {
            logger.LogInformation("Generating campaign setup template for identifier: {Identifier}", identifier);
            
            var template = await campaignService.GenerateSetupTemplateAsync(identifier, expertId, productId, publicId);
            
            return Ok(new SetupTemplateResponse
            {
                Success = true,
                Template = template,
                Message = "Campaign setup template generated successfully",
                EntityType = "Campaign",
                Identifier = identifier
            });
        }
        catch (KeyNotFoundException ex)
        {
            logger.LogWarning(ex, "Campaign not found with identifier: {Identifier}", identifier);
            return NotFound(new SetupTemplateResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating campaign setup template for identifier: {Identifier}", identifier);
            return StatusCode(500, new SetupTemplateResponse
            {
                Success = false,
                Message = "Internal server error while generating template"
            });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateCampaign([FromBody] CreateCampaignDto createCampaignDto)
    {
        try
        {
            logger.LogInformation("Creating new campaign.");
            var campaign = new Domain.Entities.Campaign
            {
                Name = createCampaignDto.Name,
                Ideia = createCampaignDto.Ideia,
                Emotion = createCampaignDto.Emotion,
                Belief = createCampaignDto.Belief,
                Type = createCampaignDto.Type,
                ConscienceLevel = createCampaignDto.ConscienceLevel,
                SophisticationLevel = createCampaignDto.SophisticationLevel
            };

            var createdCampaign = await campaignService.CreateCampaignAsync(campaign);
            return CreatedAtAction(nameof(GetCampaign), new { identifier = createdCampaign.ExternalId }, createdCampaign);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating campaign.");
            return StatusCode(500, new { Message = "Internal server error while creating campaign." });
        }
    }
}