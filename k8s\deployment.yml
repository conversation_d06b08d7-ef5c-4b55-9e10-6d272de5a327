apiVersion: apps/v1
kind: Deployment
metadata:
  name: high-copt-service-deployment
  labels:
    app: high-copt-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: high-copt-service
  template:
    metadata:
      labels:
        app: high-copt-service
        version: v1
    spec:
      containers:
      - name: high-copt-service
        image: gcr.io/highcapital-470117/high-copt-service:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:8080"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /docs
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /docs
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        imagePullPolicy: Always
      restartPolicy: Always
