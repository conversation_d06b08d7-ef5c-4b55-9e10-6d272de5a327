using Domain.Enums.Campaing;

namespace Api.DTOs;

public class CreateCampaignDto
{
    public required string Name { get; set; }
    public required string Ideia { get; set; }
    public required string Emotion { get; set; }
    public required string Belief { get; set; }
    public required CampaingTypeEnum Type { get; set; }
    public required CampaingConscienceLevelEnum ConscienceLevel { get; set; }
    public required CampaingSophisticationLevelEnum SophisticationLevel { get; set; }
}