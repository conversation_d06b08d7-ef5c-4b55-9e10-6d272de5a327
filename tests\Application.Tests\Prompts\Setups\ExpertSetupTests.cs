using Application.Prompts.Setups;
using Domain.Entities;
using Xunit;

namespace Application.Tests.Prompts.Setups;

public class ExpertSetupTests
{
    [Fact]
    public void ExpertSetupPrompt_ShouldRenderTemplateWithExpertData_ExpertType()
    {
        // Arrange
        var expert = new Expert
        {
            Name = "<PERSON>. <PERSON>",
            AreaOfExpertise = "Marketing Digital e Growth Hacking",
            Biography = "Fundador de 3 startups de sucesso, começou como desenvolvedor e descobriu o poder do marketing digital após quase falir",
            Moment = "Maior vulnerabilidade foi quando perdeu 80% dos clientes em 2019 devido à dependência de uma única estratégia",
            Dolor = "Passou anos vendo empresas falirem por não conseguirem gerar leads qualificados de forma consistente",
            Credibility = "15 anos de experiência, 500+ empresas ajudadas, palestrante TEDx, autor de 2 livros bestseller",
            Recognition = "Reconhecido pela Forbes como Top 30 Under 30 em Marketing, entrevistado pela CNN e Globo",
            TrackRecord = "Ajudou empresas a aumentarem receita em média 300% em 12 meses, com cases documentados",
            HowProductWorks = "Método Silva de Growth Sustentável - sistema de 5 pilares para crescimento escalável",
            VoicePersonality = "Direto ao ponto, provocador mas empático, usa dados e storytelling",
            EssentialValues = "Transparência radical, resultados mensuráveis, crescimento sustentável",
            Enterprise = "Silva Growth Labs"
        };

        // Act
        var result = ExpertSetup.ExpertSetupPrompt(expert);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        // Verificar se os dados do expert estão no resultado
        Assert.Contains("Dr. João Silva", result);
        Assert.Contains("Marketing Digital e Growth Hacking", result);
        Assert.Contains("Silva Growth Labs", result);
        Assert.Contains("Método Silva de Growth Sustentável", result);
        
        // Verificar estrutura do template
        Assert.Contains("BRIEFING DE EXPERT/EMPRESA", result);
        Assert.Contains("IDENTIFICAÇÃO BÁSICA:", result);
        Assert.Contains("AUTORIDADE E CREDIBILIDADE:", result);
        Assert.Contains("VOZ E PERSONALIDADE:", result);
        
        // Verificar se não há variáveis não renderizadas
        Assert.DoesNotContain("{{", result);
        Assert.DoesNotContain("}}", result);
    }

    [Fact]
    public void CompanySetupPrompt_ShouldRenderTemplateWithCompanyData()
    {
        // Arrange
        var company = new Expert
        {
            Name = "CEO Maria Santos",
            Enterprise = "TechSolutions Brasil",
            AreaOfExpertise = "Transformação Digital para PMEs",
            Biography = "Empresa fundada em 2015 após os fundadores perceberem que PMEs brasileiras estavam ficando para trás na era digital",
            Moment = "Momento atual focado em expansão para o interior do Brasil",
            Dolor = "Nasceu da frustração dos fundadores ao verem pequenas empresas fechando por não se adaptarem ao digital",
            Credibility = "Mais de 1000 empresas digitalizadas, parceria com Google e Microsoft, certificações internacionais",
            Recognition = "Prêmio Startup do Ano 2023, cases publicados em Harvard Business Review",
            TrackRecord = "95% das empresas atendidas aumentaram vendas online em 6 meses, ROI médio de 400%",
            HowProductWorks = "Plataforma TRANSFORME - metodologia proprietária de digitalização em 90 dias",
            VoicePersonality = "Técnico mas acessível, focado em resultados práticos, linguagem de negócios",
            EssentialValues = "Inovação acessível, crescimento inclusivo, tecnologia humanizada"
        };

        // Act  
        var result = ExpertSetup.CompanySetupPrompt(company);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        // Verificar se os dados da empresa estão no resultado
        Assert.Contains("TechSolutions Brasil", result);
        Assert.Contains("Transformação Digital para PMEs", result);
        Assert.Contains("Plataforma TRANSFORME", result);
        
        // Verificar se não há variáveis não renderizadas
        Assert.DoesNotContain("{{", result);
        Assert.DoesNotContain("}}", result);
    }

    [Fact]
    public void GenerateSetupPrompt_ShouldReturnExpertPromptWhenExpertTypeProvided()
    {
        // Arrange
        var expert = new Expert
        {
            Name = "Test Expert",
            AreaOfExpertise = "Test Area",
            Biography = "Test Bio",
            Moment = "Test Moment",
            Dolor = "Test Pain",
            Credibility = "Test Cred",
            Recognition = "Test Recognition",
            TrackRecord = "Test Track",
            HowProductWorks = "Test Method",
            VoicePersonality = "Test Voice",
            EssentialValues = "Test Values",
            Enterprise = "Test Company"
        };

        // Act
        var result = ExpertSetup.GenerateSetupPrompt(expert, "Expert");

        // Assert
        Assert.NotNull(result);
        Assert.Contains("ESPECIALISTA", result);
        Assert.Contains("Test Expert", result);
    }

    [Fact]
    public void GenerateSetupPrompt_ShouldReturnCompanyPromptWhenCompanyTypeProvided()
    {
        // Arrange
        var expert = new Expert
        {
            Name = "Test CEO",
            Enterprise = "Test Company",
            AreaOfExpertise = "Test Area",
            Biography = "Test Bio",
            Moment = "Test Moment",
            Dolor = "Test Pain",
            Credibility = "Test Cred",
            Recognition = "Test Recognition",
            TrackRecord = "Test Track",
            HowProductWorks = "Test Method",
            VoicePersonality = "Test Voice",
            EssentialValues = "Test Values"
        };

        // Act
        var result = ExpertSetup.GenerateSetupPrompt(expert, "Company");

        // Assert
        Assert.NotNull(result);
        Assert.Contains("EMPRESA", result);
        Assert.Contains("Test Company", result);
    }

    [Fact]
    public void GenerateSetupPrompt_ShouldHandleUnknownExpertType()
    {
        // Arrange
        var expert = new Expert
        {
            Name = "Test Expert",
            AreaOfExpertise = "Test Area",
            Biography = "Test Bio",
            Moment = "Test Moment",
            Dolor = "Test Pain",
            Credibility = "Test Cred",
            Recognition = "Test Recognition",
            TrackRecord = "Test Track",
            HowProductWorks = "Test Method",
            VoicePersonality = "Test Voice",
            EssentialValues = "Test Values",
            Enterprise = "Test Company"
        };

        // Act
        var result = ExpertSetup.GenerateSetupPrompt(expert, "InvalidType");

        // Assert
        Assert.NotNull(result);
        Assert.Contains("Tipo não selecionado", result);
    }
}
