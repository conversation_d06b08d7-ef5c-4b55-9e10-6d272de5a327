using Domain.Interfaces.Services;
using Domain.Interfaces;
using HighCapital.Core.Services;

namespace Application.Services;

public class IdGenerationService : IIdGenerationService
{
    public string GenerateExternalId()
    {
        return IdGenService.GetId();
    }

    public async Task<int> GenerateUniqueIdAsync<T>(Func<Task<IEnumerable<T>>> getAllEntitiesFunc) where T : class, IEntity
    {
        var existingEntities = await getAllEntitiesFunc();
        return existingEntities.Any() ? existingEntities.Max(e => e.Id) + 1 : 1;
    }
}
