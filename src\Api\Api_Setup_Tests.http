### Testes dos Endpoints de Setup - Clean Architecture

### 1. Teste Expert Setup - Gerar template
GET http://localhost:5000/api/expert-setup/1/template
Content-Type: application/json

### 2. Teste Expert Setup - Gerar template com tipo específico
GET http://localhost:5000/api/expert-setup/expert-123/template?expertType=marketing
Content-Type: application/json

### 3. Teste Expert Setup - Buscar expert por ID
GET http://localhost:5000/api/expert-setup/1
Content-Type: application/json

### 4. Teste Expert Setup - Buscar expert por External ID
GET http://localhost:5000/api/expert-setup/expert-ext-456
Content-Type: application/json

### 5. Teste Campaign Setup - Gerar template básico
GET http://localhost:5000/api/campaign-setup/1/template
Content-Type: application/json

### 6. Teste Campaign Setup - Gerar template com relacionamentos
GET http://localhost:5000/api/campaign-setup/campaign-123/template?expertId=1&productId=2&publicId=3
Content-Type: application/json

### 7. Teste Campaign Setup - Buscar campaign
GET http://localhost:5000/api/campaign-setup/campaign-456
Content-Type: application/json

### 8. Teste Product Setup - Gerar template
GET http://localhost:5000/api/product-setup/product-789/template
Content-Type: application/json

### 9. Teste Product Setup - Buscar product
GET http://localhost:5000/api/product-setup/1
Content-Type: application/json

### 10. Teste Public Setup - Gerar template
GET http://localhost:5000/api/public-setup/public-abc/template
Content-Type: application/json

### 11. Teste Public Setup - Buscar public
GET http://localhost:5000/api/public-setup/2
Content-Type: application/json

### 12. Teste Expert Setup - ID inexistente (deve retornar 404)
GET http://localhost:5000/api/expert-setup/999999
Content-Type: application/json

### 13. Teste Campaign Setup - Template com IDs inexistentes
GET http://localhost:5000/api/campaign-setup/999999/template?expertId=999&productId=999&publicId=999
Content-Type: application/json

### 14. Teste Campaign Setup - Criar Campanha
POST http://localhost:5000/api/campaign-setup
Content-Type: application/json

{
  "name": "Nova Campanha2",
  "ideia": "Ideia da campanha2",
  "emotion": "Emoção da campanha2",
  "belief": "Crença da campanha2",
  "type": 2,
  "conscienceLevel": 2,
  "sophisticationLevel": 2
}

### 15. Teste Expert Setup - Criar Expert
POST http://localhost:5000/api/expert-setup
Content-Type: application/json

{
  "name": "Novo Expert",
  "areaOfExpertise": "Área de atuação",
  "biography": "Biografia do expert",
  "moment": "Momento do expert",
  "dolor": "Dor do expert",
  "credibility": "Credibilidade do expert",
  "recognition": "Reconhecimento do expert",
  "trackRecord": "Histórico do expert",
  "howProductWorks": "Como o produto funciona",
  "voicePersonality": "Personalidade da voz",
  "essentialValues": "Valores essenciais",
  "enterprise": "Empresa"
}

### 16. Teste Product Setup - Criar Produto
POST http://localhost:5000/api/product-setup
Content-Type: application/json

{
  "name": "Novo Produto",
  "benefits": "Benefícios do produto",
  "socialProof": "Prova social do produto",
  "metodology": "Metodologia do produto",
  "guarantee": "Garantia do produto",
  "deliverables": 0,
  "customerJourney": 0
}

### 17. Teste Public Setup - Criar Publico
POST http://localhost:5000/api/public-setup
Content-Type: application/json

{
  "name": "Novo Publico",
  "local": "Local do publico",
  "familySituation": "Situação familiar",
  "personality": "Personalidade do publico",
  "hobbies": "Hobbies do publico",
  "lifestyle": "Estilo de vida do publico",
  "personalValue": "Valor pessoal do publico",
  "roof": "Teto do publico",
  "nextLevel": "Próximo nível do publico",
  "dropOfWater": "Gota d'água do publico",
  "beliefs": "Crenças do publico",
  "selfVisions": "Visões de si mesmo do publico",
  "possibleObjections": "Possíveis objeções do publico",
  "ownCommunication": "Comunicação própria do publico"
}