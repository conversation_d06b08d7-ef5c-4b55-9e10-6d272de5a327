using Application;
using Google.Cloud.Firestore;
using Infrastructure;
using Scalar.AspNetCore;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.WriteIndented = true;
    });

builder.Services.AddOpenApi();

builder.Services.AddInfrastructureServices();
builder.Services.AddApplicationServices();

var firestorePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "firestore-credentials.json");
builder.Services.AddSingleton(new FirestoreDbBuilder
{
    ProjectId = "highcapital-470117",
    DatabaseId = "high-copt-dev",
    
    Credential = Google.Apis.Auth.OAuth2.GoogleCredential.FromFile(firestorePath).CreateScoped("https://www.googleapis.com/auth/datastore")
}.Build());

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference("/docs");
    app.UseHttpsRedirection();
}

app.UseHttpsRedirection();
app.MapControllers();

app.Run();

public partial class Program { }