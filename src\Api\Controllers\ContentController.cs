using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Api.DTOs;
using Application.Services;
using Domain.Entities;
using Domain.Enums.Campaing;
using Domain.Enums.Products;

namespace Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ContentController(
    IContentService contentService, 
    ICompleteSetupService completeSetupService,
    ICampaignService campaignService,
    IExpertService expertService,
    IProductService productService,
    IPublicService publicService,
    ILogger<ContentController> logger) : ControllerBase
{
    [HttpPost("generate")]
    public async Task<IActionResult> GenerateContent([FromBody] GenerateContentRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Prompt))
            {
                return BadRequest(new GenerateContentResponse
                {
                    Success = false,
                    Message = "Prompt cannot be empty"
                });
            }

            logger.LogInformation("Content generation request received with provider: {Provider}", 
                request.PreferredProvider ?? "chatgpt (default)");
            
            var result = await contentService.GenerateContentAsync(request.Prompt, request.PreferredProvider);
            
            return Ok(new GenerateContentResponse
            {
                Content = result,
                Success = true,
                Message = "Content generated successfully",
                Provider = request.PreferredProvider ?? "default"
            });
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(new GenerateContentResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating content");
            return StatusCode(500, new GenerateContentResponse
            {
                Success = false,
                Message = "An error occurred while generating content"
            });
        }
    }

    [HttpPost("analyze-file")]
    public async Task<IActionResult> AnalyzeFile(IFormFile file, [FromForm] string? provider = null)
    {
        try
        {
            if (file.Length == 0)
            {
                return BadRequest(new FileAnalysisResponse
                {
                    Success = false,
                    Message = "No file was uploaded"
                });
            }

            var analysis = await contentService.AnalyzeFileAsync(file);

            return Ok(new FileAnalysisResponse
            {
                Success = true,
                Message = "File analyzed successfully",
                FileName = file.FileName,
                FileSize = file.Length,
                Analysis = analysis,
                Provider = provider ?? "default"
            });
        }
        catch (NotSupportedException ex)
        {
            logger.LogWarning(ex, "File type not supported: {FileName}", file.FileName);
            return BadRequest(new FileAnalysisResponse
            {
                Success = false,
                Message = ex.Message,
                FileName = file.FileName
            });
        }
        catch (InvalidOperationException ex)
        {
            logger.LogError(ex, "Error processing file: {FileName}", file.FileName);
            return StatusCode(500, new FileAnalysisResponse
            {
                Success = false,
                Message = ex.Message,
                FileName = file.FileName
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error analyzing file: {FileName}", file.FileName);
            return StatusCode(500, new FileAnalysisResponse
            {
                Success = false,
                Message = "An unexpected error occurred while analyzing the file",
                FileName = file.FileName
            });
        }
    }

    [HttpPost("generate-complete-copy")]
    public async Task<IActionResult> GenerateCompleteCopy([FromBody] GenerateCompleteCopyRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.CopyRequest))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = "Copy request cannot be empty"
                });
            }
            
            if (!Enum.IsDefined(typeof(CampaingTypeEnum), request.Campaign.Type))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Invalid campaign type: {request.Campaign.Type}. Valid values: {string.Join(", ", Enum.GetValues<CampaingTypeEnum>().Cast<int>())}"
                });
            }

            if (!Enum.IsDefined(typeof(CampaingConscienceLevelEnum), request.Campaign.ConscienceLevel))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Invalid conscience level: {request.Campaign.ConscienceLevel}. Valid values: {string.Join(", ", Enum.GetValues<CampaingConscienceLevelEnum>().Cast<int>())}"
                });
            }

            if (!Enum.IsDefined(typeof(CampaingSophisticationLevelEnum), request.Campaign.SophisticationLevel))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Invalid sophistication level: {request.Campaign.SophisticationLevel}. Valid values: {string.Join(", ", Enum.GetValues<CampaingSophisticationLevelEnum>().Cast<int>())}"
                });
            }

            if (!Enum.IsDefined(typeof(ProductDeliverablesEnum), request.Product.Deliverables))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Invalid product deliverables: {request.Product.Deliverables}. Valid values: {string.Join(", ", Enum.GetValues<ProductDeliverablesEnum>().Cast<int>())}"
                });
            }

            if (!Enum.IsDefined(typeof(ProductCustomerJourneyEnum), request.Product.CustomerJourney))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Invalid customer journey: {request.Product.CustomerJourney}. Valid values: {string.Join(", ", Enum.GetValues<ProductCustomerJourneyEnum>().Cast<int>())}"
                });
            }

            logger.LogInformation("Generating complete copy: {CopyRequest}", request.CopyRequest);
            
            var campaign = new Campaign
            {
                Name = request.Campaign.Name,
                Type = request.Campaign.Type,
                ConscienceLevel = request.Campaign.ConscienceLevel,
                SophisticationLevel = request.Campaign.SophisticationLevel,
                Ideia = request.Campaign.Ideia,
                Emotion = request.Campaign.Emotion,
                Belief = request.Campaign.Belief
            };

            var expert = new Expert
            {
                Name = request.Expert.Name,
                AreaOfExpertise = request.Expert.AreaOfExpertise,
                Biography = request.Expert.Biography,
                Moment = request.Expert.Moment,
                Dolor = request.Expert.Dolor,
                Credibility = request.Expert.Credibility,
                Recognition = request.Expert.Recognition,
                TrackRecord = request.Expert.TrackRecord,
                HowProductWorks = request.Expert.HowProductWorks,
                VoicePersonality = request.Expert.VoicePersonality,
                EssentialValues = request.Expert.EssentialValues,
                Enterprise = request.Expert.Enterprise
            };

            var product = new Product
            {
                Name = request.Product.Name,
                Benefits = request.Product.Benefits,
                SocialProof = request.Product.SocialProof,
                Metodology = request.Product.Metodology,
                Guarantee = request.Product.Guarantee,
                Deliverables = request.Product.Deliverables,
                CustomerJourney = request.Product.CustomerJourney
            };

            var publicTarget = new Public
            {
                Name = request.Public.Name,
                Local = request.Public.Local,
                FamilySituation = request.Public.FamilySituation,
                Personality = request.Public.Personality,
                Hobbies = request.Public.Hobbies,
                Lifestyle = request.Public.Lifestyle,
                PersonalValue = request.Public.PersonalValue,
                Roof = request.Public.Roof,
                NextLevel = request.Public.NextLevel,
                DropOfWater = request.Public.DropOfWater,
                Beliefs = request.Public.Beliefs,
                SelfVisions = request.Public.SelfVisions,
                PossibleObjections = request.Public.PossibleObjections,
                OwnCommunication = request.Public.OwnCommunication,
                EducationLevel = request.Public.PublicEducationLevel,
                Gender = request.Public.PublicGender
            };

            // Gerar copy completa usando todos os setups
            var generatedCopy = await completeSetupService.GenerateCompleteCopyAsync(
                campaign, expert, product, publicTarget, request.CopyRequest, request.Provider);

            return Ok(new GenerateCompleteCopyResponse
            {
                Success = true,
                Message = "Complete copy generated successfully",
                GeneratedCopy = generatedCopy,
                CopyRequest = request.CopyRequest,
                Provider = request.Provider ?? "default"
            });
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning("Invalid enum value in request: {Message}", ex.Message);
            return BadRequest(new GenerateCompleteCopyResponse
            {
                Success = false,
                Message = $"Invalid enum value: {ex.Message}"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating complete copy");
            return StatusCode(500, new GenerateCompleteCopyResponse
            {
                Success = false,
                Message = "An error occurred while generating the copy"
            });
        }
    }

    [HttpPost("generate-complete-copy-by-id")]
    public async Task<IActionResult> GenerateCompleteCopyById([FromBody] GenerateCompleteCopyByIdRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.CopyRequest))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = "Copy request cannot be empty"
                });
            }

            if (string.IsNullOrWhiteSpace(request.CampaignId) ||
                string.IsNullOrWhiteSpace(request.ExpertId) ||
                string.IsNullOrWhiteSpace(request.ProductId) ||
                string.IsNullOrWhiteSpace(request.PublicId))
            {
                return BadRequest(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = "All setup IDs (Campaign, Expert, Product, Public) are required"
                });
            }

            logger.LogInformation("Generating complete copy by IDs - Campaign: {CampaignId}, Expert: {ExpertId}, Product: {ProductId}, Public: {PublicId}, Request: {CopyRequest}", 
                request.CampaignId, request.ExpertId, request.ProductId, request.PublicId, request.CopyRequest);

            // Buscar as entidades pelos IDs fornecidos
            Campaign campaign;
            Expert expert;
            Product product;
            Public publicTarget;

            try
            {
                campaign = await campaignService.GetByIdOrExternalIdAsync(request.CampaignId);
            }
            catch (KeyNotFoundException)
            {
                return NotFound(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Campaign not found with ID: {request.CampaignId}"
                });
            }

            try
            {
                expert = await expertService.GetByIdOrExternalIdAsync(request.ExpertId);
            }
            catch (KeyNotFoundException)
            {
                return NotFound(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Expert not found with ID: {request.ExpertId}"
                });
            }

            try
            {
                product = await productService.GetByIdOrExternalIdAsync(request.ProductId);
            }
            catch (KeyNotFoundException)
            {
                return NotFound(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Product not found with ID: {request.ProductId}"
                });
            }

            try
            {
                publicTarget = await publicService.GetByIdOrExternalIdAsync(request.PublicId);
            }
            catch (KeyNotFoundException)
            {
                return NotFound(new GenerateCompleteCopyResponse
                {
                    Success = false,
                    Message = $"Public not found with ID: {request.PublicId}"
                });
            }

            // Gerar copy completa usando os setups recuperados
            var generatedCopy = await completeSetupService.GenerateCompleteCopyAsync(
                campaign, expert, product, publicTarget, request.CopyRequest, request.Provider);

            return Ok(new GenerateCompleteCopyResponse
            {
                Success = true,
                Message = "Complete copy generated successfully using setup IDs",
                GeneratedCopy = generatedCopy,
                CopyRequest = request.CopyRequest,
                Provider = request.Provider ?? "default"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating complete copy by IDs");
            return StatusCode(500, new GenerateCompleteCopyResponse
            {
                Success = false,
                Message = "An error occurred while generating the copy"
            });
        }
    }

    [HttpGet("providers")]
    public IActionResult GetProviders()
    {
        return Ok(new[]
        {
            new { Name = "chatgpt", DisplayName = "ChatGPT (OpenAI)", IsDefault = true },
            new { Name = "gemini", DisplayName = "Gemini (Google)", IsDefault = false }
        });
    }

    [HttpGet("health")]
    public IActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "Content API",
            Version = "1.0.0",
            CheckedAt = DateTime.UtcNow
        });
    }
}
